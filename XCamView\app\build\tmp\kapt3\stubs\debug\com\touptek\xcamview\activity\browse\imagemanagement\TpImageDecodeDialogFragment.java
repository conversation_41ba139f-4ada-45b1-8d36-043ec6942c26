package com.touptek.xcamview.activity.browse.imagemanagement;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\u0018\u0000 /2\u00020\u0001:\u0001/B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0016\u001a\u00020\u0017H\u0002J\b\u0010\u0018\u001a\u00020\u000bH\u0002J\b\u0010\u0019\u001a\u00020\u0017H\u0002J\b\u0010\u001a\u001a\u00020\u0017H\u0002J\u0012\u0010\u001b\u001a\u00020\u00172\b\u0010\u001c\u001a\u0004\u0018\u00010\u001dH\u0016J$\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020!2\b\u0010\"\u001a\u0004\u0018\u00010#2\b\u0010\u001c\u001a\u0004\u0018\u00010\u001dH\u0016J\b\u0010$\u001a\u00020\u0017H\u0016J\b\u0010%\u001a\u00020\u0017H\u0016J\u001a\u0010&\u001a\u00020\u00172\u0006\u0010\'\u001a\u00020\u001f2\b\u0010\u001c\u001a\u0004\u0018\u00010\u001dH\u0016J\b\u0010(\u001a\u00020\u0017H\u0002J\b\u0010)\u001a\u00020\u0017H\u0002J\b\u0010*\u001a\u00020\u0017H\u0002J\b\u0010+\u001a\u00020\u0017H\u0002J\b\u0010,\u001a\u00020\u0017H\u0002J\b\u0010-\u001a\u00020\u0017H\u0002J\b\u0010.\u001a\u00020\u0017H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0014\u001a\u0004\u0018\u00010\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00060"}, d2 = {"Lcom/touptek/xcamview/activity/browse/imagemanagement/TpImageDecodeDialogFragment;", "Lcom/touptek/xcamview/util/BaseDialogFragment;", "()V", "ANIMATION_DURATION", "", "MIN_FLING_VELOCITY", "", "MIN_SWIPE_DISTANCE", "binding", "Lcom/touptek/xcamview/databinding/ImageViewerBinding;", "buttonsVisible", "", "currentPosition", "", "gestureDetector", "Landroid/view/GestureDetector;", "imagePaths", "", "", "isAnimating", "switchAnimator", "Landroid/animation/ValueAnimator;", "initGestureDetector", "", "isImageAtMinimumScale", "loadCurrentImage", "loadCurrentImageWithAnimation", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onDestroy", "onStart", "onViewCreated", "view", "setupGestureDetection", "showNextImage", "showNextImageWithAnimation", "showPreviousImage", "showPreviousImageWithAnimation", "toggleButtons", "updateButtonStates", "Companion", "app_debug"})
public final class TpImageDecodeDialogFragment extends com.touptek.xcamview.util.BaseDialogFragment {
    private com.touptek.xcamview.databinding.ImageViewerBinding binding;
    private boolean buttonsVisible = false;
    private java.util.List<java.lang.String> imagePaths;
    private int currentPosition = 0;
    private android.view.GestureDetector gestureDetector;
    private boolean isAnimating = false;
    private android.animation.ValueAnimator switchAnimator;
    private final float MIN_FLING_VELOCITY = 1000.0F;
    private final float MIN_SWIPE_DISTANCE = 100.0F;
    private final long ANIMATION_DURATION = 300L;
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.xcamview.activity.browse.imagemanagement.TpImageDecodeDialogFragment.Companion Companion = null;
    private static final java.lang.String ARG_IMAGE_PATHS = "image_paths";
    private static final java.lang.String ARG_CURRENT_POSITION = "current_position";
    
    public TpImageDecodeDialogFragment() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override
    public void onViewCreated(@org.jetbrains.annotations.NotNull
    android.view.View view, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override
    public void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override
    public void onDestroy() {
    }
    
    @java.lang.Override
    public void onStart() {
    }
    
    private final void loadCurrentImage() {
    }
    
    private final void showPreviousImage() {
    }
    
    private final void showNextImage() {
    }
    
    private final void showPreviousImageWithAnimation() {
    }
    
    private final void showNextImageWithAnimation() {
    }
    
    private final void updateButtonStates() {
    }
    
    private final void toggleButtons() {
    }
    
    /**
     * 初始化手势检测器
     */
    private final void initGestureDetector() {
    }
    
    /**
     * 设置手势检测
     */
    private final void setupGestureDetection() {
    }
    
    /**
     * 检查图片是否处于最小缩放状态
     */
    private final boolean isImageAtMinimumScale() {
        return false;
    }
    
    /**
     * 带动画的图片加载
     */
    private final void loadCurrentImageWithAnimation() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001c\u0010\u0006\u001a\u00020\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00040\t2\u0006\u0010\n\u001a\u00020\u000bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/touptek/xcamview/activity/browse/imagemanagement/TpImageDecodeDialogFragment$Companion;", "", "()V", "ARG_CURRENT_POSITION", "", "ARG_IMAGE_PATHS", "newInstance", "Lcom/touptek/xcamview/activity/browse/imagemanagement/TpImageDecodeDialogFragment;", "imagePaths", "", "currentPosition", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.touptek.xcamview.activity.browse.imagemanagement.TpImageDecodeDialogFragment newInstance(@org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> imagePaths, int currentPosition) {
            return null;
        }
    }
}