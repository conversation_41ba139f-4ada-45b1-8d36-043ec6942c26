{"logs": [{"outputFile": "com.touptek.xcamview.app-mergeDebugResources-39:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\2052e42c61716abd4b6d39c7be558473\\transformed\\material-1.10.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,773,878,957,1022,1111,1176,1235,1321,1385,1449,1512,1585,1649,1703,1815,1873,1935,1989,2061,2183,2270,2356,2496,2573,2654,2781,2872,2949,3003,3054,3120,3190,3267,3354,3429,3500,3577,3646,3715,3822,3913,3985,4074,4163,4237,4309,4395,4445,4524,4590,4670,4754,4816,4880,4943,5012,5112,5207,5299,5391,5449,5504", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,81,77,76,85,83,93,104,78,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,85,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83", "endOffsets": "267,349,427,504,590,674,768,873,952,1017,1106,1171,1230,1316,1380,1444,1507,1580,1644,1698,1810,1868,1930,1984,2056,2178,2265,2351,2491,2568,2649,2776,2867,2944,2998,3049,3115,3185,3262,3349,3424,3495,3572,3641,3710,3817,3908,3980,4069,4158,4232,4304,4390,4440,4519,4585,4665,4749,4811,4875,4938,5007,5107,5202,5294,5386,5444,5499,5583"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3086,3168,3246,3323,3409,3493,3587,3692,3771,3836,3925,3990,4049,4135,4199,4263,4326,4399,4463,4517,4629,4687,4749,4803,4875,4997,5084,5170,5310,5387,5468,5595,5686,5763,5817,5868,5934,6004,6081,6168,6243,6314,6391,6460,6529,6636,6727,6799,6888,6977,7051,7123,7209,7259,7338,7404,7484,7568,7630,7694,7757,7826,7926,8021,8113,8205,8263,8318", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "endColumns": "12,81,77,76,85,83,93,104,78,64,88,64,58,85,63,63,62,72,63,53,111,57,61,53,71,121,86,85,139,76,80,126,90,76,53,50,65,69,76,86,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,83", "endOffsets": "317,3163,3241,3318,3404,3488,3582,3687,3766,3831,3920,3985,4044,4130,4194,4258,4321,4394,4458,4512,4624,4682,4744,4798,4870,4992,5079,5165,5305,5382,5463,5590,5681,5758,5812,5863,5929,5999,6076,6163,6238,6309,6386,6455,6524,6631,6722,6794,6883,6972,7046,7118,7204,7254,7333,7399,7479,7563,7625,7689,7752,7821,7921,8016,8108,8200,8258,8313,8397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\be4ee3b3d380c351331d5de220cf8d41\\transformed\\core-1.9.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "8492", "endColumns": "100", "endOffsets": "8588"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\560db8d7606c3580fcf519784f6f6a65\\transformed\\appcompat-1.6.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,911,1002,1095,1198,1293,1393,1486,1581,1677,1768,1858,1947,2057,2161,2267,2378,2482,2600,2763,2869", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,825,906,997,1090,1193,1288,1388,1481,1576,1672,1763,1853,1942,2052,2156,2262,2373,2477,2595,2758,2864,2954"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,441,551,658,744,848,968,1047,1128,1219,1312,1415,1510,1610,1703,1798,1894,1985,2075,2164,2274,2378,2484,2595,2699,2817,2980,8402", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "436,546,653,739,843,963,1042,1123,1214,1307,1410,1505,1605,1698,1793,1889,1980,2070,2159,2269,2373,2479,2590,2694,2812,2975,3081,8487"}}]}]}