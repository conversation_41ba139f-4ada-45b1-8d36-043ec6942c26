<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_image_compare" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\activity_image_compare.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_image_compare_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="128" endOffset="14"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="16" startOffset="8" endLine="22" endOffset="45"/></Target><Target id="@+id/btn_sync" view="ImageButton"><Expressions/><location startLine="35" startOffset="8" endLine="41" endOffset="47"/></Target><Target id="@+id/btn_reset" view="ImageButton"><Expressions/><location startLine="43" startOffset="8" endLine="49" endOffset="45"/></Target><Target id="@+id/btn_swap" view="ImageButton"><Expressions/><location startLine="51" startOffset="8" endLine="57" endOffset="47"/></Target><Target id="@+id/compare_container" view="LinearLayout"><Expressions/><location startLine="62" startOffset="4" endLine="126" endOffset="18"/></Target><Target id="@+id/left_image" view="com.touptek.ui.TpImageView"><Expressions/><location startLine="74" startOffset="12" endLine="78" endOffset="46"/></Target><Target id="@+id/tv_left_info" view="TextView"><Expressions/><location startLine="80" startOffset="12" endLine="91" endOffset="41"/></Target><Target id="@+id/right_image" view="com.touptek.ui.TpImageView"><Expressions/><location startLine="105" startOffset="12" endLine="109" endOffset="46"/></Target><Target id="@+id/tv_right_info" view="TextView"><Expressions/><location startLine="111" startOffset="12" endLine="122" endOffset="41"/></Target></Targets></Layout>