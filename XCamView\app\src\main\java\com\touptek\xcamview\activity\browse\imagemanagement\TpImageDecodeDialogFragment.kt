package com.touptek.xcamview.activity.browse.imagemanagement

import android.animation.ValueAnimator
import android.graphics.Matrix
import android.os.Bundle
import android.view.GestureDetector
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.view.animation.DecelerateInterpolator
import android.widget.ImageView
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.R
import com.touptek.xcamview.util.BaseDialogFragment
import com.touptek.xcamview.databinding.ImageViewerBinding
import com.touptek.ui.TpImageView

import com.touptek.video.internal.TpImageLoader
import java.util.ArrayList
import kotlin.apply
import kotlin.collections.indices
import kotlin.let
import kotlin.run
import kotlin.math.abs

class TpImageDecodeDialogFragment : BaseDialogFragment() {
    private lateinit var binding: ImageViewerBinding
    private var buttonsVisible = false
    private lateinit var imagePaths: List<String>
    private var currentPosition = 0

    // 手势检测相关
    private lateinit var gestureDetector: GestureDetector
    private var isAnimating = false
    private var switchAnimator: ValueAnimator? = null

    // 手势检测参数
    private val MIN_FLING_VELOCITY = 1000f  // 最小滑动速度
    private val MIN_SWIPE_DISTANCE = 100f   // 最小滑动距离
    private val ANIMATION_DURATION = 300L   // 动画时长




    companion object {
        private const val ARG_IMAGE_PATHS = "image_paths"
        private const val ARG_CURRENT_POSITION = "current_position"

        fun newInstance(imagePaths: List<String>, currentPosition: Int): TpImageDecodeDialogFragment {
            val fragment = TpImageDecodeDialogFragment()
            val args = Bundle().apply {
                putStringArrayList(ARG_IMAGE_PATHS, ArrayList(imagePaths))
                putInt(ARG_CURRENT_POSITION, currentPosition)
            }
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = ImageViewerBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 获取传递的参数
        imagePaths = arguments?.getStringArrayList(ARG_IMAGE_PATHS) ?: run {
            dismiss()
            return
        }
        currentPosition = arguments?.getInt(ARG_CURRENT_POSITION, 0) ?: 0

        // 初始化手势检测器
        initGestureDetector()

        // 加载当前图片
        loadCurrentImage()

        // 初始化按钮面板为隐藏状态
        binding.buttonPanel.visibility = View.GONE

        // 设置TpImageView的单击监听器来切换按钮可见性
        binding.imageView.setOnSingleTapListener {
            toggleButtons()
        }

        // 设置手势检测
        setupGestureDetection()

        // 按钮点击事件
        binding.btnPrevious.setOnClickListener {
            showPreviousImageWithAnimation()
        }

        binding.btnNext.setOnClickListener {
            showNextImageWithAnimation()
        }

        binding.btnBack.setOnClickListener {
            dismiss()
        }


    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialog) // 全屏样式
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理动画资源
        switchAnimator?.cancel()
        switchAnimator = null
    }

    override fun onStart() {
        super.onStart()
        // 设置对话框全屏
        dialog?.window?.let { window ->
            window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        }
    }

    private fun loadCurrentImage() {
        if (currentPosition in imagePaths.indices) {
            // 直接使用TpImageLoader静态方法加载高质量图片
            TpImageLoader.loadFullImage(imagePaths[currentPosition], binding.imageView)
            updateButtonStates()
        }
    }

    private fun showPreviousImage() {
        if (currentPosition > 0) {
            currentPosition--
            loadCurrentImage()
        }
    }

    private fun showNextImage() {
        if (currentPosition < imagePaths.size - 1) {
            currentPosition++
            loadCurrentImage()
        }
    }

    private fun showPreviousImageWithAnimation() {
        if (currentPosition > 0 && !isAnimating) {
            currentPosition--
            loadCurrentImageWithAnimation()
        }
    }

    private fun showNextImageWithAnimation() {
        if (currentPosition < imagePaths.size - 1 && !isAnimating) {
            currentPosition++
            loadCurrentImageWithAnimation()
        }
    }

    private fun updateButtonStates() {
        // 更新上一页按钮状态
        binding.btnPrevious.isEnabled = currentPosition > 0
        binding.btnPrevious.alpha = if (currentPosition > 0) 1.0f else 0.5f

        // 更新下一页按钮状态
        binding.btnNext.isEnabled = currentPosition < imagePaths.size - 1
        binding.btnNext.alpha = if (currentPosition < imagePaths.size - 1) 1.0f else 0.5f

        // 更新标题或其他UI元素（如果需要）
        // 例如：binding.tvImageCounter.text = "${currentPosition + 1}/${imagePaths.size}"
    }

    private fun toggleButtons() {
        buttonsVisible = !buttonsVisible
        binding.buttonPanel.visibility = if (buttonsVisible) View.VISIBLE else View.GONE
    }

    /**
     * 初始化手势检测器
     */
    private fun initGestureDetector() {
        gestureDetector = GestureDetector(requireContext(), object : GestureDetector.SimpleOnGestureListener() {
            override fun onFling(
                e1: MotionEvent?,
                e2: MotionEvent,
                velocityX: Float,
                velocityY: Float
            ): Boolean {
                if (isAnimating) return false

                // 检查是否为水平滑动
                val deltaX = e2.x - (e1?.x ?: 0f)
                val deltaY = e2.y - (e1?.y ?: 0f)

                // 判断是否为有效的水平滑动
                if (abs(deltaX) > MIN_SWIPE_DISTANCE &&
                    abs(velocityX) > MIN_FLING_VELOCITY &&
                    abs(deltaX) > abs(deltaY)) {

                    // 检查图片是否处于最小缩放状态
                    if (isImageAtMinimumScale()) {
                        if (deltaX > 0) {
                            // 右滑 - 显示上一张图片
                            if (currentPosition > 0) {
                                showPreviousImageWithAnimation()
                                return true
                            }
                        } else {
                            // 左滑 - 显示下一张图片
                            if (currentPosition < imagePaths.size - 1) {
                                showNextImageWithAnimation()
                                return true
                            }
                        }
                    }
                }
                return false
            }
        })
    }

    /**
     * 设置手势检测
     */
    private fun setupGestureDetection() {
        binding.imageView.setOnTouchListener { view, event ->
            // 如果正在动画中，不处理任何手势
            if (isAnimating) {
                return@setOnTouchListener true
            }

            // 先让手势检测器处理
            val gestureHandled = gestureDetector.onTouchEvent(event)

            // 如果手势检测器处理了事件（比如检测到滑动切换），则消费事件
            if (gestureHandled) {
                return@setOnTouchListener true
            }

            // 否则让TpImageView继续处理其他手势（缩放、平移等）
            return@setOnTouchListener false
        }
    }

    /**
     * 检查图片是否处于最小缩放状态
     */
    private fun isImageAtMinimumScale(): Boolean {
        try {
            val currentScale = binding.imageView.getCurrentScale()
            val baseScale = binding.imageView.getBaseScale()
            // 允许一定的误差范围，如果当前缩放接近基础缩放，则认为是最小缩放状态
            return currentScale <= baseScale * 1.1f
        } catch (e: Exception) {
            // 如果获取缩放状态失败，默认允许滑动切换
            return true
        }
    }

    /**
     * 带动画的图片加载
     */
    private fun loadCurrentImageWithAnimation() {
        if (isAnimating) return

        isAnimating = true

        // 取消之前的动画
        switchAnimator?.cancel()

        // 创建淡出淡入动画
        switchAnimator = ValueAnimator.ofFloat(1.0f, 0.0f, 1.0f).apply {
            duration = ANIMATION_DURATION
            interpolator = DecelerateInterpolator()

            addUpdateListener { animator ->
                val alpha = animator.animatedValue as Float
                binding.imageView.alpha = alpha

                // 在动画中点加载新图片
                if (alpha <= 0.0f && currentPosition in imagePaths.indices) {
                    TpImageLoader.loadFullImage(imagePaths[currentPosition], binding.imageView)
                    updateButtonStates()
                }
            }

            addListener(object : android.animation.AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: android.animation.Animator) {
                    isAnimating = false
                    binding.imageView.alpha = 1.0f
                }

                override fun onAnimationCancel(animation: android.animation.Animator) {
                    isAnimating = false
                    binding.imageView.alpha = 1.0f
                }
            })

            start()
        }
    }

}