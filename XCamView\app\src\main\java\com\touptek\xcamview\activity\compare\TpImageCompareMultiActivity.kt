package com.touptek.xcamview.activity.compare

import android.content.Context
import android.content.Intent
import android.graphics.Matrix
import android.os.Bundle
import android.util.Log
import android.widget.ImageButton
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.touptek.xcamview.R
import com.touptek.video.TpVideoSystem
import com.touptek.video.TpVideoConfig
import com.touptek.ui.TpImageView
import com.touptek.xcamview.util.setupEdgeToEdgeFullScreen
import android.graphics.BitmapFactory
import java.io.File

/**
 * 多图对比Activity - 支持2-4张图片对比
 * 支持全局同步、分组同步、独立操作三种模式
 */
class TpImageCompareMultiActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "TpImageCompareMulti"
        private const val EXTRA_IMAGE_PATHS = "extra_image_paths"
        
        fun start(context: Context, imagePaths: List<String>) {
            val intent = Intent(context, TpImageCompareMultiActivity::class.java)
            intent.putStringArrayListExtra(EXTRA_IMAGE_PATHS, ArrayList(imagePaths))
            context.startActivity(intent)
        }
    }
    
    // 同步模式枚举
    enum class SyncMode {
        GLOBAL,     // 全局同步 - 4张图片完全同步
        GROUP,      // 分组同步 - 上下两组分别同步
        INDEPENDENT // 独立模式 - 每张图片独立操作
    }
    
    // UI组件
    private lateinit var btnBack: ImageButton
    private lateinit var btnSyncMode: ImageButton
    private lateinit var btnReset: ImageButton
    
    private lateinit var imageView1: TpImageView
    private lateinit var imageView2: TpImageView
    private lateinit var imageView3: TpImageView
    private lateinit var imageView4: TpImageView
    
    private lateinit var tvInfo1: TextView
    private lateinit var tvInfo2: TextView
    private lateinit var tvInfo3: TextView
    private lateinit var tvInfo4: TextView
    
    // 数据
    private var imagePaths = mutableListOf<String>()
    private var currentSyncMode = SyncMode.GLOBAL
    private var isUpdating = false
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupEdgeToEdgeFullScreen() // 设置全屏显示
        setContentView(R.layout.activity_image_compare_multi)

        initViews()
        getImagePaths()
        loadImages()
        updateImageInfo()
        setupClickListeners()
        setupMatrixSync()
    }
    
    private fun initViews() {
        btnBack = findViewById(R.id.btn_back)
        btnSyncMode = findViewById(R.id.btn_sync_mode)
        btnReset = findViewById(R.id.btn_reset)
        
        imageView1 = findViewById(R.id.image_view_1)
        imageView2 = findViewById(R.id.image_view_2)
        imageView3 = findViewById(R.id.image_view_3)
        imageView4 = findViewById(R.id.image_view_4)
        
        tvInfo1 = findViewById(R.id.tv_info_1)
        tvInfo2 = findViewById(R.id.tv_info_2)
        tvInfo3 = findViewById(R.id.tv_info_3)
        tvInfo4 = findViewById(R.id.tv_info_4)
        
        updateSyncModeButton()
    }
    
    private fun getImagePaths() {
        imagePaths = intent.getStringArrayListExtra(EXTRA_IMAGE_PATHS)?.toMutableList() ?: mutableListOf()
        Log.d(TAG, "接收到${imagePaths.size}张图片路径")
    }
    
    private fun loadImages() {
        val config = TpVideoConfig.createDefault4K()
        val videoSystem = TpVideoSystem(this, config)
        
        val imageViews = listOf(imageView1, imageView2, imageView3, imageView4)
        
        for (i in imageViews.indices) {
            if (i < imagePaths.size) {
                // 加载图片
                try {
                    videoSystem.loadFullImage(imagePaths[i], imageViews[i])
                    Log.d(TAG, "图片${i+1}加载成功: ${imagePaths[i]}")
                } catch (e: Exception) {
                    Log.e(TAG, "图片${i+1}加载失败: ${imagePaths[i]}", e)
                }
            } else {
                // 显示空白
                imageViews[i].setImageResource(0)
                imageViews[i].setBackgroundColor(0xFF1A1A1A.toInt())
            }
        }
    }
    
    private fun updateImageInfo() {
        val infoViews = listOf(tvInfo1, tvInfo2, tvInfo3, tvInfo4)

        for (i in infoViews.indices) {
            if (i < imagePaths.size) {
                val file = File(imagePaths[i])
                val fileName = file.name
                val resolution = getImageResolution(imagePaths[i])
                infoViews[i].text = "$fileName ($resolution)"
            } else {
                infoViews[i].text = "空位置"
            }
        }
    }

    private fun getImageResolution(imagePath: String): String {
        return try {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(imagePath, options)
            val width = options.outWidth
            val height = options.outHeight
            if (width > 0 && height > 0) {
                "${width}×${height}"
            } else {
                "未知分辨率"
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取图片分辨率失败: $imagePath", e)
            "未知分辨率"
        }
    }
    
    private fun setupClickListeners() {
        btnBack.setOnClickListener {
            finish()
        }
        
        btnSyncMode.setOnClickListener {
            cycleSyncMode()
        }
        
        btnReset.setOnClickListener {
            resetImages()
        }
    }
    
    private fun cycleSyncMode() {
        currentSyncMode = when (currentSyncMode) {
            SyncMode.GLOBAL -> SyncMode.GROUP
            SyncMode.GROUP -> SyncMode.INDEPENDENT
            SyncMode.INDEPENDENT -> SyncMode.GLOBAL
        }
        
        updateSyncModeButton()
        
        val modeText = when (currentSyncMode) {
            SyncMode.GLOBAL -> "全局同步"
            SyncMode.GROUP -> "分组同步"
            SyncMode.INDEPENDENT -> "独立模式"
        }
        
        Toast.makeText(this, "切换到: $modeText", Toast.LENGTH_SHORT).show()
        Log.d(TAG, "同步模式切换到: $modeText")
    }
    
    private fun updateSyncModeButton() {
        btnSyncMode.alpha = when (currentSyncMode) {
            SyncMode.GLOBAL -> 1.0f
            SyncMode.GROUP -> 0.7f
            SyncMode.INDEPENDENT -> 0.4f
        }
    }
    
    private fun resetImages() {
        loadImages()
        Toast.makeText(this, "已重置所有图片", Toast.LENGTH_SHORT).show()
    }

    // ===== 同步功能相关方法 =====

    private fun setupMatrixSync() {
        val imageViews = listOf(imageView1, imageView2, imageView3, imageView4)

        imageViews.forEachIndexed { index, imageView ->
            imageView.setMatrixChangeListener {
                if (!isUpdating) {
                    syncMatrix(index)
                }
            }
        }
    }

    private fun syncMatrix(sourceIndex: Int) {
        if (currentSyncMode == SyncMode.INDEPENDENT) return

        isUpdating = true
        try {
            val sourceImageView = getImageViewByIndex(sourceIndex)
            val sourceMatrix = sourceImageView.imageMatrix

            when (currentSyncMode) {
                SyncMode.GLOBAL -> {
                    // 全局同步 - 同步到所有其他图片
                    syncToAllExcept(sourceIndex, sourceMatrix)
                }
                SyncMode.GROUP -> {
                    // 分组同步 - 同步到同组的图片
                    syncToGroup(sourceIndex, sourceMatrix)
                }
                SyncMode.INDEPENDENT -> {
                    // 独立模式 - 不同步
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "同步Matrix失败", e)
        } finally {
            isUpdating = false
        }
    }

    private fun syncToAllExcept(excludeIndex: Int, matrix: Matrix) {
        val imageViews = listOf(imageView1, imageView2, imageView3, imageView4)

        imageViews.forEachIndexed { index, imageView ->
            if (index != excludeIndex && index < imagePaths.size) {
                imageView.imageMatrix = Matrix(matrix)
            }
        }
    }

    private fun syncToGroup(sourceIndex: Int, matrix: Matrix) {
        // 分组规则：0,1为上组，2,3为下组
        val isTopGroup = sourceIndex < 2
        val targetIndices = if (isTopGroup) {
            listOf(0, 1).filter { it != sourceIndex }
        } else {
            listOf(2, 3).filter { it != sourceIndex }
        }

        targetIndices.forEach { targetIndex ->
            if (targetIndex < imagePaths.size) {
                val targetImageView = getImageViewByIndex(targetIndex)
                targetImageView.imageMatrix = Matrix(matrix)
            }
        }
    }

    private fun getImageViewByIndex(index: Int): TpImageView {
        return when (index) {
            0 -> imageView1
            1 -> imageView2
            2 -> imageView3
            3 -> imageView4
            else -> throw IllegalArgumentException("Invalid image view index: $index")
        }
    }
}
