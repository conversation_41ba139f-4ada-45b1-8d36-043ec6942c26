package com.touptek.xcamview.activity.compare;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u000b\u0018\u0000 &2\u00020\u0001:\u0001&B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0014\u001a\u00020\u0015H\u0002J\u0010\u0010\u0016\u001a\u00020\f2\u0006\u0010\u0017\u001a\u00020\fH\u0002J\b\u0010\u0018\u001a\u00020\u0015H\u0002J\b\u0010\u0019\u001a\u00020\u0015H\u0002J\u0012\u0010\u001a\u001a\u00020\u00152\b\u0010\u001b\u001a\u0004\u0018\u00010\u001cH\u0014J\b\u0010\u001d\u001a\u00020\u0015H\u0002J\b\u0010\u001e\u001a\u00020\u0015H\u0002J\b\u0010\u001f\u001a\u00020\u0015H\u0002J\b\u0010 \u001a\u00020\u0015H\u0002J\b\u0010!\u001a\u00020\u0015H\u0002J\b\u0010\"\u001a\u00020\u0015H\u0002J\b\u0010#\u001a\u00020\u0015H\u0002J\b\u0010$\u001a\u00020\u0015H\u0002J\b\u0010%\u001a\u00020\u0015H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\'"}, d2 = {"Lcom/touptek/xcamview/activity/compare/TpImageCompareActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "btnBack", "Landroid/widget/ImageButton;", "btnReset", "btnSwap", "btnSync", "isSyncEnabled", "", "isUpdating", "leftImagePath", "", "leftImageView", "Lcom/touptek/ui/TpImageView;", "rightImagePath", "rightImageView", "tvLeftInfo", "Landroid/widget/TextView;", "tvRightInfo", "getImagePaths", "", "getImageResolution", "imagePath", "initViews", "loadImages", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "resetImages", "setupClickListeners", "setupMatrixSync", "swapImages", "syncMatrixFromLeftToRight", "syncMatrixFromRightToLeft", "toggleSync", "updateImageInfo", "updateSyncButtonState", "Companion", "app_debug"})
public final class TpImageCompareActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.xcamview.activity.compare.TpImageCompareActivity.Companion Companion = null;
    private static final java.lang.String TAG = "TpImageCompareActivity";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String EXTRA_LEFT_IMAGE = "left_image";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String EXTRA_RIGHT_IMAGE = "right_image";
    private com.touptek.ui.TpImageView leftImageView;
    private com.touptek.ui.TpImageView rightImageView;
    private android.widget.ImageButton btnBack;
    private android.widget.ImageButton btnSync;
    private android.widget.ImageButton btnReset;
    private android.widget.ImageButton btnSwap;
    private android.widget.TextView tvLeftInfo;
    private android.widget.TextView tvRightInfo;
    private java.lang.String leftImagePath;
    private java.lang.String rightImagePath;
    private boolean isSyncEnabled = true;
    private boolean isUpdating = false;
    
    public TpImageCompareActivity() {
        super();
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initViews() {
    }
    
    private final void getImagePaths() {
    }
    
    private final void updateImageInfo() {
    }
    
    private final java.lang.String getImageResolution(java.lang.String imagePath) {
        return null;
    }
    
    private final void loadImages() {
    }
    
    private final void setupClickListeners() {
    }
    
    private final void resetImages() {
    }
    
    private final void swapImages() {
    }
    
    private final void setupMatrixSync() {
    }
    
    private final void syncMatrixFromLeftToRight() {
    }
    
    private final void syncMatrixFromRightToLeft() {
    }
    
    private final void toggleSync() {
    }
    
    private final void updateSyncButtonState() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001e\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u00042\u0006\u0010\f\u001a\u00020\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/touptek/xcamview/activity/compare/TpImageCompareActivity$Companion;", "", "()V", "EXTRA_LEFT_IMAGE", "", "EXTRA_RIGHT_IMAGE", "TAG", "start", "", "context", "Landroid/content/Context;", "leftImagePath", "rightImagePath", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        public final void start(@org.jetbrains.annotations.NotNull
        android.content.Context context, @org.jetbrains.annotations.NotNull
        java.lang.String leftImagePath, @org.jetbrains.annotations.NotNull
        java.lang.String rightImagePath) {
        }
    }
}