<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_image_compare_triple" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\activity_image_compare_triple.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_image_compare_triple_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="177" endOffset="14"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="18" startOffset="8" endLine="25" endOffset="40"/></Target><Target id="@+id/btn_swap_position" view="ImageButton"><Expressions/><location startLine="41" startOffset="8" endLine="49" endOffset="44"/></Target><Target id="@+id/btn_sync_mode" view="ImageButton"><Expressions/><location startLine="52" startOffset="8" endLine="60" endOffset="44"/></Target><Target id="@+id/btn_reset" view="ImageButton"><Expressions/><location startLine="63" startOffset="8" endLine="70" endOffset="40"/></Target><Target id="@+id/compare_container" view="LinearLayout"><Expressions/><location startLine="75" startOffset="4" endLine="175" endOffset="18"/></Target><Target id="@+id/image_view_left" view="com.touptek.ui.TpImageView"><Expressions/><location startLine="88" startOffset="12" endLine="92" endOffset="46"/></Target><Target id="@+id/tv_info_left" view="TextView"><Expressions/><location startLine="94" startOffset="12" endLine="105" endOffset="41"/></Target><Target id="@+id/image_view_center" view="com.touptek.ui.TpImageView"><Expressions/><location startLine="121" startOffset="12" endLine="125" endOffset="46"/></Target><Target id="@+id/tv_info_center" view="TextView"><Expressions/><location startLine="127" startOffset="12" endLine="138" endOffset="41"/></Target><Target id="@+id/image_view_right" view="com.touptek.ui.TpImageView"><Expressions/><location startLine="154" startOffset="12" endLine="158" endOffset="46"/></Target><Target id="@+id/tv_info_right" view="TextView"><Expressions/><location startLine="160" startOffset="12" endLine="171" endOffset="41"/></Target></Targets></Layout>