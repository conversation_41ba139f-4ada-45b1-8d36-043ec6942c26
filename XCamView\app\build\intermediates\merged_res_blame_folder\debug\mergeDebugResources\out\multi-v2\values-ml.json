{"logs": [{"outputFile": "com.touptek.xcamview.app-mergeDebugResources-39:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\2052e42c61716abd4b6d39c7be558473\\transformed\\material-1.10.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,351,434,521,627,726,820,930,1022,1087,1186,1252,1312,1414,1476,1552,1610,1688,1753,1807,1924,1988,2052,2106,2186,2320,2406,2495,2631,2716,2804,2956,3051,3134,3192,3244,3310,3389,3471,3562,3649,3725,3802,3879,3950,4060,4167,4247,4344,4444,4518,4599,4704,4762,4850,4917,5008,5100,5162,5226,5289,5358,5461,5568,5673,5778,5840,5896", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,82,86,105,98,93,109,91,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,88,135,84,87,151,94,82,57,51,65,78,81,90,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,106,104,104,61,55,83", "endOffsets": "266,346,429,516,622,721,815,925,1017,1082,1181,1247,1307,1409,1471,1547,1605,1683,1748,1802,1919,1983,2047,2101,2181,2315,2401,2490,2626,2711,2799,2951,3046,3129,3187,3239,3305,3384,3466,3557,3644,3720,3797,3874,3945,4055,4162,4242,4339,4439,4513,4594,4699,4757,4845,4912,5003,5095,5157,5221,5284,5353,5456,5563,5668,5773,5835,5891,5975"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3150,3233,3320,3426,3525,3619,3729,3821,3886,3985,4051,4111,4213,4275,4351,4409,4487,4552,4606,4723,4787,4851,4905,4985,5119,5205,5294,5430,5515,5603,5755,5850,5933,5991,6043,6109,6188,6270,6361,6448,6524,6601,6678,6749,6859,6966,7046,7143,7243,7317,7398,7503,7561,7649,7716,7807,7899,7961,8025,8088,8157,8260,8367,8472,8577,8639,8695", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "endColumns": "12,79,82,86,105,98,93,109,91,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,88,135,84,87,151,94,82,57,51,65,78,81,90,86,75,76,76,70,109,106,79,96,99,73,80,104,57,87,66,90,91,61,63,62,68,102,106,104,104,61,55,83", "endOffsets": "316,3145,3228,3315,3421,3520,3614,3724,3816,3881,3980,4046,4106,4208,4270,4346,4404,4482,4547,4601,4718,4782,4846,4900,4980,5114,5200,5289,5425,5510,5598,5750,5845,5928,5986,6038,6104,6183,6265,6356,6443,6519,6596,6673,6744,6854,6961,7041,7138,7238,7312,7393,7498,7556,7644,7711,7802,7894,7956,8020,8083,8152,8255,8362,8467,8572,8634,8690,8774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\be4ee3b3d380c351331d5de220cf8d41\\transformed\\core-1.9.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "8862", "endColumns": "100", "endOffsets": "8958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\560db8d7606c3580fcf519784f6f6a65\\transformed\\appcompat-1.6.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,428,534,645,736,841,963,1041,1116,1207,1300,1401,1495,1595,1689,1784,1883,1974,2065,2147,2256,2360,2459,2571,2683,2804,2969,8779", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "423,529,640,731,836,958,1036,1111,1202,1295,1396,1490,1590,1684,1779,1878,1969,2060,2142,2251,2355,2454,2566,2678,2799,2964,3065,8857"}}]}]}