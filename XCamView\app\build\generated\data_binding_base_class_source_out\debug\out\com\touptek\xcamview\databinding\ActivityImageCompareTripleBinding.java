// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.ui.TpImageView;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityImageCompareTripleBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final ImageButton btnReset;

  @NonNull
  public final ImageButton btnSwapPosition;

  @NonNull
  public final ImageButton btnSyncMode;

  @NonNull
  public final LinearLayout compareContainer;

  @NonNull
  public final TpImageView imageViewCenter;

  @NonNull
  public final TpImageView imageViewLeft;

  @NonNull
  public final TpImageView imageViewRight;

  @NonNull
  public final TextView tvInfoCenter;

  @NonNull
  public final TextView tvInfoLeft;

  @NonNull
  public final TextView tvInfoRight;

  private ActivityImageCompareTripleBinding(@NonNull LinearLayout rootView,
      @NonNull ImageButton btnBack, @NonNull ImageButton btnReset,
      @NonNull ImageButton btnSwapPosition, @NonNull ImageButton btnSyncMode,
      @NonNull LinearLayout compareContainer, @NonNull TpImageView imageViewCenter,
      @NonNull TpImageView imageViewLeft, @NonNull TpImageView imageViewRight,
      @NonNull TextView tvInfoCenter, @NonNull TextView tvInfoLeft, @NonNull TextView tvInfoRight) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnReset = btnReset;
    this.btnSwapPosition = btnSwapPosition;
    this.btnSyncMode = btnSyncMode;
    this.compareContainer = compareContainer;
    this.imageViewCenter = imageViewCenter;
    this.imageViewLeft = imageViewLeft;
    this.imageViewRight = imageViewRight;
    this.tvInfoCenter = tvInfoCenter;
    this.tvInfoLeft = tvInfoLeft;
    this.tvInfoRight = tvInfoRight;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityImageCompareTripleBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityImageCompareTripleBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_image_compare_triple, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityImageCompareTripleBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_reset;
      ImageButton btnReset = ViewBindings.findChildViewById(rootView, id);
      if (btnReset == null) {
        break missingId;
      }

      id = R.id.btn_swap_position;
      ImageButton btnSwapPosition = ViewBindings.findChildViewById(rootView, id);
      if (btnSwapPosition == null) {
        break missingId;
      }

      id = R.id.btn_sync_mode;
      ImageButton btnSyncMode = ViewBindings.findChildViewById(rootView, id);
      if (btnSyncMode == null) {
        break missingId;
      }

      id = R.id.compare_container;
      LinearLayout compareContainer = ViewBindings.findChildViewById(rootView, id);
      if (compareContainer == null) {
        break missingId;
      }

      id = R.id.image_view_center;
      TpImageView imageViewCenter = ViewBindings.findChildViewById(rootView, id);
      if (imageViewCenter == null) {
        break missingId;
      }

      id = R.id.image_view_left;
      TpImageView imageViewLeft = ViewBindings.findChildViewById(rootView, id);
      if (imageViewLeft == null) {
        break missingId;
      }

      id = R.id.image_view_right;
      TpImageView imageViewRight = ViewBindings.findChildViewById(rootView, id);
      if (imageViewRight == null) {
        break missingId;
      }

      id = R.id.tv_info_center;
      TextView tvInfoCenter = ViewBindings.findChildViewById(rootView, id);
      if (tvInfoCenter == null) {
        break missingId;
      }

      id = R.id.tv_info_left;
      TextView tvInfoLeft = ViewBindings.findChildViewById(rootView, id);
      if (tvInfoLeft == null) {
        break missingId;
      }

      id = R.id.tv_info_right;
      TextView tvInfoRight = ViewBindings.findChildViewById(rootView, id);
      if (tvInfoRight == null) {
        break missingId;
      }

      return new ActivityImageCompareTripleBinding((LinearLayout) rootView, btnBack, btnReset,
          btnSwapPosition, btnSyncMode, compareContainer, imageViewCenter, imageViewLeft,
          imageViewRight, tvInfoCenter, tvInfoLeft, tvInfoRight);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
