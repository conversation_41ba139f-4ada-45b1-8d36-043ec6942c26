<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="whitebalance_layout" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\whitebalance_layout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/whitebalance_layout_0" view="LinearLayout"><Expressions/><location startLine="0" startOffset="0" endLine="258" endOffset="14"/></Target><Target id="@+id/wb_btn_group" view="RadioGroup"><Expressions/><location startLine="17" startOffset="8" endLine="46" endOffset="20"/></Target><Target id="@+id/radio_auto_tv" view="RadioButton"><Expressions/><location startLine="23" startOffset="12" endLine="29" endOffset="57"/></Target><Target id="@+id/radio_manual_tv" view="RadioButton"><Expressions/><location startLine="31" startOffset="12" endLine="37" endOffset="57"/></Target><Target id="@+id/radio_roi_tv" view="RadioButton"><Expressions/><location startLine="39" startOffset="12" endLine="45" endOffset="57"/></Target><Target id="@+id/text_red_value" view="TextView"><Expressions/><location startLine="69" startOffset="12" endLine="77" endOffset="41"/></Target><Target id="@+id/btn_red_reduce" view="ImageButton"><Expressions/><location startLine="87" startOffset="12" endLine="93" endOffset="47"/></Target><Target id="@+id/seekbar_red_tv" view="SeekBar"><Expressions/><location startLine="95" startOffset="12" endLine="102" endOffset="57"/></Target><Target id="@+id/btn_red_add" view="ImageButton"><Expressions/><location startLine="104" startOffset="12" endLine="110" endOffset="47"/></Target><Target id="@+id/text_green_value" view="TextView"><Expressions/><location startLine="133" startOffset="12" endLine="141" endOffset="41"/></Target><Target id="@+id/btn_green_reduce" view="ImageButton"><Expressions/><location startLine="151" startOffset="12" endLine="157" endOffset="47"/></Target><Target id="@+id/seekbar_green_tv" view="SeekBar"><Expressions/><location startLine="159" startOffset="12" endLine="166" endOffset="57"/></Target><Target id="@+id/btn_green_add" view="ImageButton"><Expressions/><location startLine="168" startOffset="12" endLine="174" endOffset="47"/></Target><Target id="@+id/text_blue_value" view="TextView"><Expressions/><location startLine="197" startOffset="12" endLine="205" endOffset="41"/></Target><Target id="@+id/btn_blue_reduce" view="ImageButton"><Expressions/><location startLine="215" startOffset="12" endLine="221" endOffset="47"/></Target><Target id="@+id/seekbar_blue_tv" view="SeekBar"><Expressions/><location startLine="223" startOffset="12" endLine="230" endOffset="57"/></Target><Target id="@+id/btn_blue_add" view="ImageButton"><Expressions/><location startLine="232" startOffset="12" endLine="238" endOffset="47"/></Target><Target id="@+id/btn_Default_wb" view="Button"><Expressions/><location startLine="249" startOffset="8" endLine="254" endOffset="64"/></Target></Targets></Layout>