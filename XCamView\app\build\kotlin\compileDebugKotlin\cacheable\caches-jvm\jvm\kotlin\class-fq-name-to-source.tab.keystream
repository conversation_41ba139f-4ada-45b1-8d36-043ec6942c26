*com.touptek.xcamview.activity.StatusBanner*com.touptek.xcamview.activity.MainActivity&com.touptek.xcamview.activity.MainMenuDcom.touptek.xcamview.activity.MainMenu.OnRectangleVisibilityListener>com.touptek.xcamview.activity.MainMenu.MenuPopupDialogFragment3com.touptek.xcamview.activity.MainMenu.ButtonAction1com.touptek.xcamview.activity.MainMenu.MenuAction-com.android.rockchip.camera2.view.OverlayView
FolderAdapterFolderAdapter.ViewHolder<com.touptek.xcamview.activity.browse.TpCopyDirDialogFragmentScom.touptek.xcamview.activity.browse.TpCopyDirDialogFragment.OnMoveCompleteListenerFcom.touptek.xcamview.activity.browse.TpCopyDirDialogFragment.Companion:com.touptek.xcamview.activity.browse.TpOperationDirAdapterEcom.touptek.xcamview.activity.browse.TpOperationDirAdapter.ViewHolder7com.touptek.xcamview.activity.browse.TpThumbGridAdapterBcom.touptek.xcamview.activity.browse.TpThumbGridAdapter.ViewHolder=com.touptek.xcamview.activity.browse.TpThumbSpacingDecoration2com.touptek.xcamview.activity.browse.TpVideoBrowse<<EMAIL>[com.touptek.xcamview.activity.browse.videomanagement.TpVideoDecoderDialogFragment.Companion<com.touptek.xcamview.activity.compare.TpImageCompareActivityFcom.touptek.xcamview.activity.compare.TpImageCompareActivity.CompanionAcom.touptek.xcamview.activity.compare.TpImageCompareMultiActivityKcom.touptek.xcamview.activity.compare.TpImageCompareMultiActivity.CompanionJcom.touptek.xcamview.activity.compare.TpImageCompareMultiActivity.SyncModeBcom.touptek.xcamview.activity.compare.TpImageCompareTripleActivityLcom.touptek.xcamview.activity.compare.TpImageCompareTripleActivity.CompanionKcom.touptek.xcamview.activity.compare.TpImageCompareTripleActivity.SyncModeBcom.touptek.xcamview.activity.ispdialogfragment.TpAEDialogFragmentDcom.touptek.xcamview.activity.ispdialogfragment.TpFlipDialogFragmentBcom.touptek.xcamview.activity.ispdialogfragment.TpHzDialogFragmentMcom.touptek.xcamview.activity.ispdialogfragment.TpImageProcess2DialogFragmentLcom.touptek.xcamview.activity.ispdialogfragment.TpImageProcessDialogFragmentEcom.touptek.xcamview.activity.ispdialogfragment.TpSceneDialogFragmentBcom.touptek.xcamview.activity.ispdialogfragment.TpWBDialogFragmentVcom.touptek.xcamview.activity.ispdialogfragment.wbroimanagement.TpRectangleOverlayView]com.touptek.xcamview.activity.ispdialogfragment.wbroimanagement.TpRectangleOverlayView.CornerEcom.touptek.xcamview.activity.measurement.TpMeasurementDialogFragment?com.touptek.xcamview.activity.settings.TpFormatSettingsFragmentIcom.touptek.xcamview.activity.settings.TpFormatSettingsFragment.CompanionDcom.touptek.xcamview.activity.settings.TpMeasurementSettingsFragment=<EMAIL>?com.touptek.xcamview.activity.settings.TpRecordSettingsFragment?<EMAIL>#com.touptek.xcamview.util.FontUtils&com.touptek.xcamview.util.BaseActivity,com.touptek.xcamview.util.BaseDialogFragment&com.touptek.xcamview.util.BaseFragment0com.touptek.xcamview.view.MeasurementOverlayView5com.touptek.xcamview.view.MeasurementOverlayView.Mode6com.touptek.xcamview.view.MeasurementOverlayView.Shape5com.touptek.xcamview.view.MeasurementOverlayView.Line7com.touptek.xcamview.view.MeasurementOverlayView.Circle4com.touptek.xcamview.databinding.ActivityMainBinding8com.touptek.xcamview.databinding.RightPanelLayoutBinding3com.touptek.xcamview.databinding.ImageViewerBinding4com.touptek.xcamview.databinding.BrowseLayoutBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                