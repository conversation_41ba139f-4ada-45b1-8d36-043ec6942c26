# 手势切换图片功能实现说明

## 功能概述

已成功在 `TpImageDecodeDialogFragment` 中实现了手势切换图片功能，用户现在可以通过左右滑动手势来切换图片，同时保持了原有的缩放和平移功能。

## 实现的功能

### 1. 手势检测
- **左滑**：切换到下一张图片
- **右滑**：切换到上一张图片
- **手势阈值**：
  - 最小滑动距离：100像素
  - 最小滑动速度：1000像素/秒
  - 必须是水平滑动（水平距离 > 垂直距离）

### 2. 智能手势冲突处理
- **图片最小缩放状态**：启用左右滑动切换
- **图片放大状态**：禁用滑动切换，优先处理图片的缩放和平移
- **动画期间**：禁用所有手势，避免冲突

### 3. 切换动画效果
- **淡入淡出动画**：当前图片淡出，新图片淡入
- **动画时长**：300毫秒
- **动画插值器**：DecelerateInterpolator（减速效果）

### 4. 边界处理
- **第一张图片**：禁用右滑（向前切换）
- **最后一张图片**：禁用左滑（向后切换）
- **边界保护**：防止越界访问

## 测试步骤

### 基本功能测试

1. **打开图片浏览器**
   - 在文件浏览界面选择一个包含多张图片的文件夹
   - 点击任意图片打开图片查看器

2. **测试左右滑动切换**
   - 确保图片处于适应屏幕大小状态（最小缩放）
   - 向左快速滑动 → 应该切换到下一张图片
   - 向右快速滑动 → 应该切换到上一张图片
   - 观察切换动画效果（淡入淡出）

3. **测试手势冲突处理**
   - 双击图片放大
   - 尝试左右滑动 → 应该移动图片而不是切换图片
   - 双击图片缩小到适应屏幕
   - 再次尝试左右滑动 → 应该切换图片

4. **测试边界情况**
   - 在第一张图片时向右滑动 → 不应该有反应
   - 在最后一张图片时向左滑动 → 不应该有反应

### 高级功能测试

5. **测试动画中断**
   - 快速连续滑动切换图片
   - 确保动画能正确中断和重新开始

6. **测试按钮功能兼容性**
   - 点击屏幕显示控制按钮
   - 使用上一页/下一页按钮切换图片
   - 确保按钮切换也有动画效果

## 技术实现细节

### 核心文件修改
- `TpImageDecodeDialogFragment.kt`：主要实现文件

### 新增的关键方法
- `initGestureDetector()`：初始化手势检测器
- `setupGestureDetection()`：设置手势检测
- `isImageAtMinimumScale()`：检查图片缩放状态
- `loadCurrentImageWithAnimation()`：带动画的图片加载
- `showPreviousImageWithAnimation()`：带动画的上一张图片
- `showNextImageWithAnimation()`：带动画的下一张图片

### 手势检测参数
```kotlin
private val MIN_FLING_VELOCITY = 1000f  // 最小滑动速度
private val MIN_SWIPE_DISTANCE = 100f   // 最小滑动距离
private val ANIMATION_DURATION = 300L   // 动画时长
```

## 预期效果

用户体验应该类似于Android系统相册：
1. 在图片适应屏幕时，可以通过滑动切换图片
2. 在图片放大时，滑动用于移动图片
3. 切换时有流畅的淡入淡出动画
4. 在边界图片时，滑动无效果

## 故障排除

如果功能不正常，请检查：
1. TpImageView的getCurrentScale()和getBaseScale()方法是否正常工作
2. 手势检测参数是否合适（可能需要根据设备调整）
3. 动画是否被正确取消和重新开始
4. 图片列表是否正确传递给DialogFragment

## 后续优化建议

1. 可以添加滑动进度指示器
2. 可以添加触觉反馈（震动）
3. 可以优化动画效果（如平移动画）
4. 可以添加滑动距离不足时的回弹效果
