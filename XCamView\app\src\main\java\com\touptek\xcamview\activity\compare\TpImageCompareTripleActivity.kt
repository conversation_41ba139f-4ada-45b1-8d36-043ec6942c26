package com.touptek.xcamview.activity.compare

import android.content.Context
import android.content.Intent
import android.graphics.Matrix
import android.os.Bundle
import android.util.Log
import android.widget.ImageButton
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.touptek.xcamview.R
import com.touptek.video.TpVideoSystem
import com.touptek.video.TpVideoConfig
import com.touptek.ui.TpImageView
import com.touptek.xcamview.util.setupEdgeToEdgeFullScreen
import android.graphics.BitmapFactory
import java.io.File

/**
 * 三图对比Activity - 支持2+1布局的三张图片对比
 * 支持全局同步、分组同步、独立操作三种模式
 */
class TpImageCompareTripleActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "TpImageCompareTriple"
        private const val EXTRA_IMAGE_PATHS = "extra_image_paths"
        
        fun start(context: Context, imagePaths: List<String>) {
            if (imagePaths.size != 3) {
                Toast.makeText(context, "需要提供3张图片路径", Toast.LENGTH_SHORT).show()
                return
            }
            val intent = Intent(context, TpImageCompareTripleActivity::class.java)
            intent.putStringArrayListExtra(EXTRA_IMAGE_PATHS, ArrayList(imagePaths))
            context.startActivity(intent)
        }
    }
    
    // 同步模式枚举
    enum class SyncMode {
        GLOBAL,     // 全局同步 - 三张图片完全同步
        GROUP,      // 分组同步 - 左中两图同步，右侧独立
        INDEPENDENT // 独立模式 - 三张图片各自独立
    }
    
    // UI组件
    private lateinit var btnBack: ImageButton
    private lateinit var btnSyncMode: ImageButton
    private lateinit var btnReset: ImageButton
    private lateinit var btnSwapPosition: ImageButton
    
    private lateinit var imageViewLeft: TpImageView
    private lateinit var imageViewCenter: TpImageView
    private lateinit var imageViewRight: TpImageView

    private lateinit var tvInfoLeft: TextView
    private lateinit var tvInfoCenter: TextView
    private lateinit var tvInfoRight: TextView
    
    // 数据
    private var imagePaths = mutableListOf<String>()
    private var currentSyncMode = SyncMode.GLOBAL
    private var isUpdating = false
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupEdgeToEdgeFullScreen() // 设置全屏显示
        setContentView(R.layout.activity_image_compare_triple)

        initViews()
        getImagePaths()
        loadImages()
        updateImageInfo()
        setupClickListeners()
        setupMatrixSync()
    }
    
    private fun initViews() {
        btnBack = findViewById(R.id.btn_back)
        btnSyncMode = findViewById(R.id.btn_sync_mode)
        btnReset = findViewById(R.id.btn_reset)
        btnSwapPosition = findViewById(R.id.btn_swap_position)
        
        imageViewLeft = findViewById(R.id.image_view_left)
        imageViewCenter = findViewById(R.id.image_view_center)
        imageViewRight = findViewById(R.id.image_view_right)

        tvInfoLeft = findViewById(R.id.tv_info_left)
        tvInfoCenter = findViewById(R.id.tv_info_center)
        tvInfoRight = findViewById(R.id.tv_info_right)
        
        updateSyncModeButton()
    }
    
    private fun getImagePaths() {
        imagePaths = intent.getStringArrayListExtra(EXTRA_IMAGE_PATHS)?.toMutableList() ?: mutableListOf()
        Log.d(TAG, "接收到${imagePaths.size}张图片路径")
        
        if (imagePaths.size != 3) {
            Toast.makeText(this, "图片数量不正确，需要3张图片", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
    }
    
    private fun loadImages() {
        val config = TpVideoConfig.createDefault4K()
        val videoSystem = TpVideoSystem(this, config)
        
        val imageViews = listOf(imageViewLeft, imageViewCenter, imageViewRight)
        
        for (i in imageViews.indices) {
            if (i < imagePaths.size) {
                try {
                    videoSystem.loadFullImage(imagePaths[i], imageViews[i])
                    Log.d(TAG, "图片${i+1}加载成功: ${imagePaths[i]}")
                } catch (e: Exception) {
                    Log.e(TAG, "图片${i+1}加载失败: ${imagePaths[i]}", e)
                }
            }
        }
    }
    
    private fun updateImageInfo() {
        val infoViews = listOf(tvInfoLeft, tvInfoCenter, tvInfoRight)

        for (i in infoViews.indices) {
            if (i < imagePaths.size) {
                val file = File(imagePaths[i])
                val fileName = file.name
                val resolution = getImageResolution(imagePaths[i])
                infoViews[i].text = "$fileName ($resolution)"
            } else {
                infoViews[i].text = "无图片"
            }
        }
    }

    private fun getImageResolution(imagePath: String): String {
        return try {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(imagePath, options)
            val width = options.outWidth
            val height = options.outHeight
            if (width > 0 && height > 0) {
                "${width}×${height}"
            } else {
                "未知分辨率"
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取图片分辨率失败: $imagePath", e)
            "未知分辨率"
        }
    }
    
    private fun setupClickListeners() {
        btnBack.setOnClickListener { finish() }
        btnSyncMode.setOnClickListener { cycleSyncMode() }
        btnReset.setOnClickListener { resetImages() }
        btnSwapPosition.setOnClickListener { swapImagePositions() }
    }
    
    private fun cycleSyncMode() {
        currentSyncMode = when (currentSyncMode) {
            SyncMode.GLOBAL -> SyncMode.GROUP
            SyncMode.GROUP -> SyncMode.INDEPENDENT
            SyncMode.INDEPENDENT -> SyncMode.GLOBAL
        }
        
        updateSyncModeButton()
        
        val modeText = when (currentSyncMode) {
            SyncMode.GLOBAL -> "全局同步"
            SyncMode.GROUP -> "分组同步"
            SyncMode.INDEPENDENT -> "独立模式"
        }
        
        Toast.makeText(this, "切换到: $modeText", Toast.LENGTH_SHORT).show()
        Log.d(TAG, "同步模式切换到: $modeText")
    }
    
    private fun updateSyncModeButton() {
        btnSyncMode.alpha = when (currentSyncMode) {
            SyncMode.GLOBAL -> 1.0f
            SyncMode.GROUP -> 0.7f
            SyncMode.INDEPENDENT -> 0.4f
        }
    }
    
    private fun resetImages() {
        loadImages()
        Toast.makeText(this, "已重置所有图片", Toast.LENGTH_SHORT).show()
    }
    
    private fun swapImagePositions() {
        // 实现三张图片的循环位置切换：左侧 -> 中间 -> 右侧 -> 左侧
        val temp = imagePaths[0]
        imagePaths[0] = imagePaths[1]
        imagePaths[1] = imagePaths[2]
        imagePaths[2] = temp

        loadImages()
        updateImageInfo()
        Toast.makeText(this, "图片位置已切换", Toast.LENGTH_SHORT).show()
        Log.d(TAG, "图片位置切换完成")
    }

    // ===== 同步功能相关方法 =====

    private fun setupMatrixSync() {
        val imageViews = listOf(imageViewLeft, imageViewCenter, imageViewRight)

        imageViews.forEachIndexed { index, imageView ->
            imageView.setMatrixChangeListener {
                if (!isUpdating) {
                    syncMatrix(index)
                }
            }
        }
    }

    private fun syncMatrix(sourceIndex: Int) {
        if (currentSyncMode == SyncMode.INDEPENDENT) return

        isUpdating = true
        try {
            val sourceImageView = getImageViewByIndex(sourceIndex)
            val sourceMatrix = sourceImageView.imageMatrix

            when (currentSyncMode) {
                SyncMode.GLOBAL -> {
                    // 全局同步 - 同步到所有其他图片
                    syncToAllExcept(sourceIndex, sourceMatrix)
                }
                SyncMode.GROUP -> {
                    // 分组同步 - 上方两图同步，下方独立
                    syncToGroup(sourceIndex, sourceMatrix)
                }
                SyncMode.INDEPENDENT -> {
                    // 独立模式 - 不同步
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "同步Matrix失败", e)
        } finally {
            isUpdating = false
        }
    }

    private fun syncToAllExcept(excludeIndex: Int, matrix: Matrix) {
        val imageViews = listOf(imageViewLeft, imageViewCenter, imageViewRight)

        imageViews.forEachIndexed { index, imageView ->
            if (index != excludeIndex) {
                imageView.imageMatrix = Matrix(matrix)
            }
        }
    }

    private fun syncToGroup(sourceIndex: Int, matrix: Matrix) {
        // 分组规则：0,1为左组（左侧、中间），2为右组（右侧独立）
        when (sourceIndex) {
            0 -> { // 左侧图片变化，同步到中间
                imageViewCenter.imageMatrix = Matrix(matrix)
            }
            1 -> { // 中间图片变化，同步到左侧
                imageViewLeft.imageMatrix = Matrix(matrix)
            }
            2 -> { // 右侧图片变化，不同步到其他图片（独立）
                // 右侧图片在分组模式下独立操作
            }
        }
    }

    private fun getImageViewByIndex(index: Int): TpImageView {
        return when (index) {
            0 -> imageViewLeft
            1 -> imageViewCenter
            2 -> imageViewRight
            else -> throw IllegalArgumentException("Invalid image view index: $index")
        }
    }
}
